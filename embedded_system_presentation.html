<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基于STM32F429的嵌入式数据采集系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/c.min.js"></script>
    <style>
        :root {
            --tech-blue: #00AEEF;
            --tech-blue-light: rgba(0, 174, 239, 0.7);
            --tech-blue-lighter: rgba(0, 174, 239, 0.3);
        }
        
        body {
            background: #000000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .tech-gradient {
            background: linear-gradient(135deg, var(--tech-blue-light), var(--tech-blue-lighter));
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 174, 239, 0.2);
        }
        
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }
        
        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .tech-number {
            color: var(--tech-blue);
            text-shadow: 0 0 20px rgba(0, 174, 239, 0.5);
        }
        
        .code-card {
            background: #1a1a1a;
            border: 1px solid #333;
        }
        
        .code-card pre {
            margin: 0;
            background: transparent;
        }
        
        .code-card code {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.85rem;
            line-height: 1.4;
        }
    </style>
</head>
<body class="text-white">
    <!-- 主标题区域 -->
    <div class="container mx-auto px-6 py-12">
        <div class="text-center mb-16 fade-in">
            <h1 class="text-6xl font-bold mb-4 tech-number">基于STM32F429的嵌入式数据采集系统</h1>
            <p class="text-2xl text-gray-400">STM32F429-Based Embedded Data Acquisition System</p>
            <div class="mt-8 flex justify-center space-x-8">
                <div class="text-center">
                    <div class="text-4xl font-bold tech-number">120MHz</div>
                    <div class="text-sm text-gray-500">System Clock</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold tech-number">12-bit</div>
                    <div class="text-sm text-gray-500">ADC Resolution</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold tech-number">460.8K</div>
                    <div class="text-sm text-gray-500">UART Baud Rate</div>
                </div>
            </div>
        </div>

        <!-- 第一部分：工程任务分析 -->
        <section class="mb-20 fade-in">
            <div class="bg-gray-900 rounded-xl p-8 border border-gray-700">
                <h2 class="text-4xl font-bold mb-8 tech-number flex items-center">
                    <i class="fas fa-tasks mr-4"></i>第一部分：工程任务分析
                </h2>
                <p class="text-xl text-gray-300 mb-8">Project Task Analysis</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-gray-800 rounded-lg p-6 card-hover border border-gray-600">
                        <div class="text-center">
                            <i class="fas fa-microchip text-4xl tech-number mb-4"></i>
                            <h3 class="text-2xl font-bold mb-2">硬件平台</h3>
                            <p class="text-sm text-gray-400 mb-3">Hardware Platform</p>
                            <p class="text-gray-300">STM32F429ZIT6 Cortex-M4内核，192KB SRAM，2MB Flash存储</p>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800 rounded-lg p-6 card-hover border border-gray-600">
                        <div class="text-center">
                            <i class="fas fa-chart-line text-4xl tech-number mb-4"></i>
                            <h3 class="text-2xl font-bold mb-2">数据采集</h3>
                            <p class="text-sm text-gray-400 mb-3">Data Acquisition</p>
                            <p class="text-gray-300">12位ADC高精度采样，DMA传输，2048点缓冲区</p>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800 rounded-lg p-6 card-hover border border-gray-600">
                        <div class="text-center">
                            <i class="fas fa-database text-4xl tech-number mb-4"></i>
                            <h3 class="text-2xl font-bold mb-2">数据存储</h3>
                            <p class="text-sm text-gray-400 mb-3">Data Storage</p>
                            <p class="text-gray-300">双文件系统：SD卡FATFS + SPI Flash LFS</p>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800 rounded-lg p-6 card-hover border border-gray-600">
                        <div class="text-center">
                            <i class="fas fa-wifi text-4xl tech-number mb-4"></i>
                            <h3 class="text-2xl font-bold mb-2">通信接口</h3>
                            <p class="text-sm text-gray-400 mb-3">Communication</p>
                            <p class="text-gray-300">UART/I2C/SPI/SDIO多协议支持</p>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800 rounded-lg p-6 card-hover border border-gray-600">
                        <div class="text-center">
                            <i class="fas fa-desktop text-4xl tech-number mb-4"></i>
                            <h3 class="text-2xl font-bold mb-2">人机界面</h3>
                            <p class="text-sm text-gray-400 mb-3">HMI Interface</p>
                            <p class="text-gray-300">128×32 OLED显示，WouoUI图形界面</p>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800 rounded-lg p-6 card-hover border border-gray-600">
                        <div class="text-center">
                            <i class="fas fa-cogs text-4xl tech-number mb-4"></i>
                            <h3 class="text-2xl font-bold mb-2">实时控制</h3>
                            <p class="text-sm text-gray-400 mb-3">Real-time Control</p>
                            <p class="text-gray-300">事件驱动调度器，低延迟响应</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 第二部分：系统单元功能分析设计 -->
        <section class="mb-20 fade-in">
            <div class="bg-gray-900 rounded-xl p-8 border border-gray-700">
                <h2 class="text-4xl font-bold mb-8 tech-number flex items-center">
                    <i class="fas fa-sitemap mr-4"></i>第二部分：系统单元功能分析设计
                </h2>
                <p class="text-xl text-gray-300 mb-8">System Unit Function Analysis & Design</p>
                
                <!-- 核心性能指标 -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-gray-800 rounded-lg p-6 text-center card-hover">
                        <div class="text-5xl font-bold tech-number mb-2">1.1μs</div>
                        <div class="text-sm text-gray-400">ADC转换时间</div>
                        <div class="text-xs text-gray-500">ADC Conversion Time</div>
                    </div>
                    <div class="bg-gray-800 rounded-lg p-6 text-center card-hover">
                        <div class="text-5xl font-bold tech-number mb-2">32</div>
                        <div class="text-sm text-gray-400">事件队列深度</div>
                        <div class="text-xs text-gray-500">Event Queue Size</div>
                    </div>
                    <div class="bg-gray-800 rounded-lg p-6 text-center card-hover">
                        <div class="text-5xl font-bold tech-number mb-2">16</div>
                        <div class="text-sm text-gray-400">最大事件处理器</div>
                        <div class="text-xs text-gray-500">Max Event Handlers</div>
                    </div>
                    <div class="bg-gray-800 rounded-lg p-6 text-center card-hover">
                        <div class="text-5xl font-bold tech-number mb-2">4KB</div>
                        <div class="text-sm text-gray-400">I2C时钟频率</div>
                        <div class="text-xs text-gray-500">I2C Clock Freq</div>
                    </div>
                </div>

                <!-- 功能模块卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-gray-800 rounded-lg p-6 card-hover border border-gray-600">
                        <h3 class="text-xl font-bold mb-3 tech-number">ADC采样模块</h3>
                        <p class="text-sm text-gray-400 mb-3">ADC Sampling Module</p>
                        <ul class="text-sm text-gray-300 space-y-1">
                            <li>• 定时器触发DMA采样</li>
                            <li>• 12位分辨率，3.3V参考电压</li>
                            <li>• 2048点循环缓冲区</li>
                            <li>• 硬件过采样滤波</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gray-800 rounded-lg p-6 card-hover border border-gray-600">
                        <h3 class="text-xl font-bold mb-3 tech-number">事件总线系统</h3>
                        <p class="text-sm text-gray-400 mb-3">Event Bus System</p>
                        <ul class="text-sm text-gray-300 space-y-1">
                            <li>• 发布/订阅模式解耦</li>
                            <li>• 4级优先级事件处理</li>
                            <li>• 同步/异步事件支持</li>
                            <li>• 多订阅者广播机制</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gray-800 rounded-lg p-6 card-hover border border-gray-600">
                        <h3 class="text-xl font-bold mb-3 tech-number">调度器设计</h3>
                        <p class="text-sm text-gray-400 mb-3">Scheduler Design</p>
                        <ul class="text-sm text-gray-300 space-y-1">
                            <li>• 时间片轮询调度</li>
                            <li>• 事件驱动任务管理</li>
                            <li>• 任务优先级支持</li>
                            <li>• 实时响应保证</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 第三部分：综合系统设计 -->
        <section class="mb-20 fade-in">
            <div class="bg-gray-900 rounded-xl p-8 border border-gray-700">
                <h2 class="text-4xl font-bold mb-8 tech-number flex items-center">
                    <i class="fas fa-project-diagram mr-4"></i>第三部分：综合系统设计
                </h2>
                <p class="text-xl text-gray-300 mb-8">Integrated System Design</p>

                <!-- 系统架构图表 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    <div class="bg-gray-800 rounded-lg p-6">
                        <h3 class="text-xl font-bold mb-4 tech-number">系统架构层次</h3>
                        <canvas id="architectureChart" width="400" height="300"></canvas>
                    </div>
                    <div class="bg-gray-800 rounded-lg p-6">
                        <h3 class="text-xl font-bold mb-4 tech-number">内存使用分布</h3>
                        <canvas id="memoryChart" width="400" height="300"></canvas>
                    </div>
                </div>

                <!-- 核心代码展示 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div class="code-card rounded-lg p-6">
                        <h3 class="text-lg font-bold mb-3 tech-number">ADC DMA初始化核心函数</h3>
                        <pre><code class="language-c">void adc_tim_dma_init(void) {
    // 启动定时器3触发ADC采样
    HAL_TIM_Base_Start(&htim3);

    // 启动ADC DMA采样，2048点缓冲区
    HAL_ADC_Start_DMA(&hadc1,
        (uint32_t *)adc_val_buffer,
        BUFFER_SIZE);

    // 禁用半传输中断，优化性能
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT);
}</code></pre>
                    </div>

                    <div class="code-card rounded-lg p-6">
                        <h3 class="text-lg font-bold mb-3 tech-number">事件总线发布机制</h3>
                        <pre><code class="language-c">event_status_t event_bus_publish(
    event_type_t event_type,
    event_priority_t priority,
    const void *data,
    uint16_t data_size) {

    // 创建事件结构
    system_event_t event;
    create_event(&event, event_type,
                priority, data, data_size);

    // 直接处理事件（同步模式）
    return process_event_direct(&event);
}</code></pre>
                    </div>
                </div>

                <!-- 技术特性卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-gray-800 rounded-lg p-4 text-center card-hover">
                        <i class="fas fa-bolt text-3xl tech-number mb-2"></i>
                        <h4 class="font-bold mb-1">低功耗设计</h4>
                        <p class="text-xs text-gray-400">Power Management</p>
                        <p class="text-sm text-gray-300 mt-2">动态时钟管理，睡眠模式优化</p>
                    </div>

                    <div class="bg-gray-800 rounded-lg p-4 text-center card-hover">
                        <i class="fas fa-shield-alt text-3xl tech-number mb-2"></i>
                        <h4 class="font-bold mb-1">错误处理</h4>
                        <p class="text-xs text-gray-400">Error Handling</p>
                        <p class="text-sm text-gray-300 mt-2">统一错误码，异常恢复机制</p>
                    </div>

                    <div class="bg-gray-800 rounded-lg p-4 text-center card-hover">
                        <i class="fas fa-sync-alt text-3xl tech-number mb-2"></i>
                        <h4 class="font-bold mb-1">实时性保证</h4>
                        <p class="text-xs text-gray-400">Real-time Guarantee</p>
                        <p class="text-sm text-gray-300 mt-2">中断优先级管理，确定性响应</p>
                    </div>

                    <div class="bg-gray-800 rounded-lg p-4 text-center card-hover">
                        <i class="fas fa-expand-arrows-alt text-3xl tech-number mb-2"></i>
                        <h4 class="font-bold mb-1">模块化设计</h4>
                        <p class="text-xs text-gray-400">Modular Design</p>
                        <p class="text-sm text-gray-300 mt-2">松耦合架构，易于扩展维护</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 第四部分：工程系统优化 -->
        <section class="mb-20 fade-in">
            <div class="bg-gray-900 rounded-xl p-8 border border-gray-700">
                <h2 class="text-4xl font-bold mb-8 tech-number flex items-center">
                    <i class="fas fa-tachometer-alt mr-4"></i>第四部分：工程系统优化
                </h2>
                <p class="text-xl text-gray-300 mb-8">Engineering System Optimization</p>

                <!-- 性能优化指标 -->
                <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
                    <div class="bg-gray-800 rounded-lg p-4 text-center card-hover">
                        <div class="text-3xl font-bold tech-number mb-1">60%</div>
                        <div class="text-xs text-gray-400">耦合度降低</div>
                        <div class="text-xs text-gray-500">Coupling Reduction</div>
                    </div>
                    <div class="bg-gray-800 rounded-lg p-4 text-center card-hover">
                        <div class="text-3xl font-bold tech-number mb-1">30%</div>
                        <div class="text-xs text-gray-400">编译效率提升</div>
                        <div class="text-xs text-gray-500">Build Efficiency</div>
                    </div>
                    <div class="bg-gray-800 rounded-lg p-4 text-center card-hover">
                        <div class="text-3xl font-bold tech-number mb-1">20%</div>
                        <div class="text-xs text-gray-400">内存优化</div>
                        <div class="text-xs text-gray-500">Memory Optimization</div>
                    </div>
                    <div class="bg-gray-800 rounded-lg p-4 text-center card-hover">
                        <div class="text-3xl font-bold tech-number mb-1">15%</div>
                        <div class="text-xs text-gray-400">响应速度提升</div>
                        <div class="text-xs text-gray-500">Response Speed</div>
                    </div>
                    <div class="bg-gray-800 rounded-lg p-4 text-center card-hover">
                        <div class="text-3xl font-bold tech-number mb-1">10ms</div>
                        <div class="text-xs text-gray-400">事件响应延迟</div>
                        <div class="text-xs text-gray-500">Event Latency</div>
                    </div>
                </div>

                <!-- 优化策略 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    <div class="bg-gray-800 rounded-lg p-6 card-hover border border-gray-600">
                        <h3 class="text-xl font-bold mb-3 tech-number">事件驱动解耦</h3>
                        <p class="text-sm text-gray-400 mb-3">Event-Driven Decoupling</p>
                        <ul class="text-sm text-gray-300 space-y-1">
                            <li>• 发布/订阅模式替代直接调用</li>
                            <li>• 模块间零编译依赖</li>
                            <li>• 动态事件处理器注册</li>
                            <li>• 优先级队列调度</li>
                        </ul>
                    </div>

                    <div class="bg-gray-800 rounded-lg p-6 card-hover border border-gray-600">
                        <h3 class="text-xl font-bold mb-3 tech-number">内存池管理</h3>
                        <p class="text-sm text-gray-400 mb-3">Memory Pool Management</p>
                        <ul class="text-sm text-gray-300 space-y-1">
                            <li>• 固定大小内存块分配</li>
                            <li>• 避免内存碎片化</li>
                            <li>• 零拷贝数据传递</li>
                            <li>• 内存使用统计监控</li>
                        </ul>
                    </div>

                    <div class="bg-gray-800 rounded-lg p-6 card-hover border border-gray-600">
                        <h3 class="text-xl font-bold mb-3 tech-number">编译优化</h3>
                        <p class="text-sm text-gray-400 mb-3">Build Optimization</p>
                        <ul class="text-sm text-gray-300 space-y-1">
                            <li>• 头文件依赖拆分</li>
                            <li>• 前向声明减少包含</li>
                            <li>• 增量编译支持</li>
                            <li>• 模块独立编译</li>
                        </ul>
                    </div>
                </div>

                <!-- 性能对比图表 -->
                <div class="bg-gray-800 rounded-lg p-6">
                    <h3 class="text-xl font-bold mb-4 tech-number">优化前后性能对比</h3>
                    <canvas id="performanceChart" width="800" height="400"></canvas>
                </div>
            </div>
        </section>

        <!-- 第五部分：系统功能调试 -->
        <section class="mb-20 fade-in">
            <div class="bg-gray-900 rounded-xl p-8 border border-gray-700">
                <h2 class="text-4xl font-bold mb-8 tech-number flex items-center">
                    <i class="fas fa-bug mr-4"></i>第五部分：系统功能调试
                </h2>
                <p class="text-xl text-gray-300 mb-8">System Function Debugging</p>

                <!-- 调试工具和方法 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-gray-800 rounded-lg p-6 text-center card-hover">
                        <i class="fas fa-search text-4xl tech-number mb-3"></i>
                        <h3 class="text-lg font-bold mb-2">在线调试</h3>
                        <p class="text-xs text-gray-400 mb-2">Online Debugging</p>
                        <p class="text-sm text-gray-300">SWD接口，实时变量监控</p>
                    </div>

                    <div class="bg-gray-800 rounded-lg p-6 text-center card-hover">
                        <i class="fas fa-chart-bar text-4xl tech-number mb-3"></i>
                        <h3 class="text-lg font-bold mb-2">性能分析</h3>
                        <p class="text-xs text-gray-400 mb-2">Performance Analysis</p>
                        <p class="text-sm text-gray-300">CPU占用率，内存使用监控</p>
                    </div>

                    <div class="bg-gray-800 rounded-lg p-6 text-center card-hover">
                        <i class="fas fa-file-alt text-4xl tech-number mb-3"></i>
                        <h3 class="text-lg font-bold mb-2">日志系统</h3>
                        <p class="text-xs text-gray-400 mb-2">Logging System</p>
                        <p class="text-sm text-gray-300">分级日志，文件存储</p>
                    </div>

                    <div class="bg-gray-800 rounded-lg p-6 text-center card-hover">
                        <i class="fas fa-vial text-4xl tech-number mb-3"></i>
                        <h3 class="text-lg font-bold mb-2">单元测试</h3>
                        <p class="text-xs text-gray-400 mb-2">Unit Testing</p>
                        <p class="text-sm text-gray-300">模块独立测试验证</p>
                    </div>
                </div>

                <!-- 调试代码示例 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div class="code-card rounded-lg p-6">
                        <h3 class="text-lg font-bold mb-3 tech-number">系统状态监控函数</h3>
                        <pre><code class="language-c">void system_monitor_task(void) {
    uint8_t total_handlers, active_handlers, queue_usage;

    // 获取事件总线统计信息
    event_bus_get_stats(&total_handlers,
                        &active_handlers,
                        &queue_usage);

    // 记录系统状态到日志
    data_storage_write_log(
        "System: CPU=%d%%, MEM=%d%%, EVT=%d%%",
        get_cpu_usage(),
        get_memory_usage(),
        queue_usage);
}</code></pre>
                    </div>

                    <div class="code-card rounded-lg p-6">
                        <h3 class="text-lg font-bold mb-3 tech-number">错误处理与恢复机制</h3>
                        <pre><code class="language-c">event_status_t error_handler(
    const system_event_t *event) {

    error_info_t *error = (error_info_t*)event->data;

    switch(error->level) {
        case ERROR_LEVEL_WARNING:
            // 记录警告，继续运行
            log_warning(error->message);
            break;

        case ERROR_LEVEL_CRITICAL:
            // 关键错误，系统重启
            system_safe_restart();
            break;
    }
    return EVENT_STATUS_OK;
}</code></pre>
                    </div>
                </div>

                <!-- 测试结果展示 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-gray-800 rounded-lg p-6 text-center card-hover">
                        <div class="text-4xl font-bold tech-number mb-2">99.8%</div>
                        <div class="text-sm text-gray-400">系统稳定性</div>
                        <div class="text-xs text-gray-500">System Stability</div>
                        <p class="text-sm text-gray-300 mt-2">连续运行72小时无故障</p>
                    </div>

                    <div class="bg-gray-800 rounded-lg p-6 text-center card-hover">
                        <div class="text-4xl font-bold tech-number mb-2">85%</div>
                        <div class="text-sm text-gray-400">测试覆盖率</div>
                        <div class="text-xs text-gray-500">Test Coverage</div>
                        <p class="text-sm text-gray-300 mt-2">核心功能模块全覆盖</p>
                    </div>

                    <div class="bg-gray-800 rounded-lg p-6 text-center card-hover">
                        <div class="text-4xl font-bold tech-number mb-2">5ms</div>
                        <div class="text-sm text-gray-400">平均响应时间</div>
                        <div class="text-xs text-gray-500">Avg Response Time</div>
                        <p class="text-sm text-gray-300 mt-2">实时性能指标达标</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 总结区域 -->
        <section class="mb-20 fade-in">
            <div class="bg-gradient-to-r from-gray-900 to-gray-800 rounded-xl p-8 border border-gray-700 tech-gradient">
                <div class="text-center">
                    <h2 class="text-4xl font-bold mb-4 text-white">项目成果总结</h2>
                    <p class="text-xl text-gray-200 mb-8">Project Achievement Summary</p>

                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="text-5xl font-bold text-white mb-2">9</div>
                            <div class="text-sm text-gray-200">核心功能模块</div>
                        </div>
                        <div class="text-center">
                            <div class="text-5xl font-bold text-white mb-2">40+</div>
                            <div class="text-sm text-gray-200">系统事件类型</div>
                        </div>
                        <div class="text-center">
                            <div class="text-5xl font-bold text-white mb-2">2048</div>
                            <div class="text-sm text-gray-200">采样缓冲区大小</div>
                        </div>
                        <div class="text-center">
                            <div class="text-5xl font-bold text-white mb-2">100%</div>
                            <div class="text-sm text-gray-200">功能验证通过率</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script>
        // 初始化代码高亮
        hljs.highlightAll();

        // 滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // 图表配置
        const chartOptions = {
            responsive: true,
            plugins: {
                legend: {
                    labels: {
                        color: '#ffffff'
                    }
                }
            },
            scales: {
                y: {
                    ticks: {
                        color: '#ffffff'
                    },
                    grid: {
                        color: '#333333'
                    }
                },
                x: {
                    ticks: {
                        color: '#ffffff'
                    },
                    grid: {
                        color: '#333333'
                    }
                }
            }
        };

        // 系统架构图表
        const architectureCtx = document.getElementById('architectureChart').getContext('2d');
        new Chart(architectureCtx, {
            type: 'doughnut',
            data: {
                labels: ['应用层', '中间件', 'HAL层', '硬件层'],
                datasets: [{
                    data: [30, 25, 25, 20],
                    backgroundColor: [
                        '#00AEEF',
                        'rgba(0, 174, 239, 0.7)',
                        'rgba(0, 174, 239, 0.5)',
                        'rgba(0, 174, 239, 0.3)'
                    ],
                    borderWidth: 2,
                    borderColor: '#333'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff'
                        }
                    }
                }
            }
        });

        // 内存使用图表
        const memoryCtx = document.getElementById('memoryChart').getContext('2d');
        new Chart(memoryCtx, {
            type: 'pie',
            data: {
                labels: ['应用代码', '系统栈', 'DMA缓冲', '文件系统', '空闲'],
                datasets: [{
                    data: [45, 15, 20, 10, 10],
                    backgroundColor: [
                        '#00AEEF',
                        'rgba(0, 174, 239, 0.8)',
                        'rgba(0, 174, 239, 0.6)',
                        'rgba(0, 174, 239, 0.4)',
                        'rgba(0, 174, 239, 0.2)'
                    ],
                    borderWidth: 2,
                    borderColor: '#333'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff'
                        }
                    }
                }
            }
        });

        // 性能对比图表
        const performanceCtx = document.getElementById('performanceChart').getContext('2d');
        new Chart(performanceCtx, {
            type: 'bar',
            data: {
                labels: ['编译时间', 'CPU占用率', '内存使用', '响应延迟', '耦合度'],
                datasets: [{
                    label: '优化前',
                    data: [100, 85, 75, 25, 90],
                    backgroundColor: 'rgba(255, 99, 132, 0.6)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 1
                }, {
                    label: '优化后',
                    data: [70, 65, 60, 15, 30],
                    backgroundColor: 'rgba(0, 174, 239, 0.6)',
                    borderColor: '#00AEEF',
                    borderWidth: 1
                }]
            },
            options: chartOptions
        });

        // 页面加载完成后显示第一个元素
        window.addEventListener('load', () => {
            document.querySelector('.fade-in').classList.add('visible');
        });
    </script>
</body>
</html>
