
#ifndef __EVENT_BUS_H__
#define __EVENT_BUS_H__

#include "stdint.h"
#include "stddef.h"

#ifdef __cplusplus
extern "C" {
#endif

// 事件总线配置参数
#define EVENT_BUS_MAX_HANDLERS 16    // 最大事件处理器数量
#define EVENT_BUS_QUEUE_SIZE 32      // 事件队列大小
#define EVENT_BUS_MAX_DATA_SIZE 64   // 事件数据最大大小

// 事件优先级定义
typedef enum {
    EVENT_PRIORITY_LOW = 0,      // 低优先级事件
    EVENT_PRIORITY_NORMAL = 1,   // 普通优先级事件
    EVENT_PRIORITY_HIGH = 2,     // 高优先级事件
    EVENT_PRIORITY_CRITICAL = 3  // 关键优先级事件
} event_priority_t;

// 系统事件类型定义
typedef enum {
    // 采样相关事件
    EVENT_SAMPLING_START = 0x0100,        // 采样开始事件
    EVENT_SAMPLING_STOP = 0x0101,          // 采样停止事件
    EVENT_SAMPLING_DATA_READY = 0x0102,    // 采样数据就绪事件
    EVENT_SAMPLING_OVERLIMIT = 0x0103,     // 采样超限事件
    EVENT_SAMPLING_STATE_CHANGED = 0x0104, // 采样状态变化事件
    
    // 配置相关事件
    EVENT_CONFIG_CHANGED = 0x0200,         // 配置变更事件
    EVENT_CONFIG_SAVED = 0x0201,           // 配置保存事件
    EVENT_CONFIG_LOADED = 0x0202,          // 配置加载事件
    EVENT_CONFIG_RESET = 0x0203,           // 配置重置事件
    
    // 存储相关事件
    EVENT_STORAGE_WRITE_COMPLETE = 0x0300, // 存储写入完成事件
    EVENT_STORAGE_READ_COMPLETE = 0x0301,  // 存储读取完成事件
    EVENT_STORAGE_ERROR = 0x0302,          // 存储错误事件
    EVENT_STORAGE_FULL = 0x0303,           // 存储空间满事件
    
    // 按键相关事件
    EVENT_BUTTON_PRESSED = 0x0400,         // 按键按下事件
    EVENT_BUTTON_RELEASED = 0x0401,        // 按键释放事件
    EVENT_BUTTON_CLICK = 0x0402,           // 按键点击事件
    EVENT_BUTTON_LONG_PRESS = 0x0403,      // 按键长按事件
    
    // 系统相关事件
    EVENT_SYSTEM_INIT = 0x0500,            // 系统初始化事件
    EVENT_SYSTEM_ERROR = 0x0501,           // 系统错误事件
    EVENT_SYSTEM_WARNING = 0x0502,         // 系统警告事件
    EVENT_SYSTEM_STATUS_CHANGED = 0x0503,  // 系统状态变化事件
    
    // LED相关事件
    EVENT_LED_STATE_CHANGED = 0x0600,      // LED状态变化事件
    EVENT_LED_BLINK_START = 0x0601,        // LED闪烁开始事件
    EVENT_LED_BLINK_STOP = 0x0602,         // LED闪烁停止事件
    
    // 通信相关事件
    EVENT_UART_DATA_RECEIVED = 0x0700,     // 串口数据接收事件
    EVENT_UART_DATA_SENT = 0x0701,         // 串口数据发送事件
    EVENT_UART_ERROR = 0x0702,             // 串口错误事件
    
    EVENT_TYPE_MAX = 0xFFFF                // 事件类型最大值
} event_type_t;

// 事件状态定义
typedef enum {
    EVENT_STATUS_OK = 0,        // 事件处理成功
    EVENT_STATUS_ERROR = 1,     // 事件处理错误
    EVENT_STATUS_IGNORED = 2,   // 事件被忽略
    EVENT_STATUS_PENDING = 3    // 事件待处理
} event_status_t;

// 事件数据结构
typedef struct {
    event_type_t type;          // 事件类型
    event_priority_t priority;  // 事件优先级
    uint32_t timestamp;         // 事件时间戳
    uint16_t data_size;         // 数据大小
    uint8_t data[EVENT_BUS_MAX_DATA_SIZE]; // 事件数据
} system_event_t;

// 事件处理器函数类型定义（参考ebtn_evt_fn模式）
typedef event_status_t (*event_handler_t)(const system_event_t *event);

// 事件处理器注册结构
typedef struct {
    event_type_t event_type;    // 监听的事件类型
    event_handler_t handler;    // 事件处理函数
    uint8_t enabled;            // 处理器是否启用
} event_handler_entry_t;

// 事件队列结构（参考WouoUI消息队列设计）
typedef struct {
    system_event_t events[EVENT_BUS_QUEUE_SIZE]; // 事件队列数组
    uint8_t front;              // 队列头指针
    uint8_t rear;               // 队列尾指针
    uint8_t count;              // 队列中事件数量
} event_queue_t;

// 事件总线主结构
typedef struct {
    event_handler_entry_t handlers[EVENT_BUS_MAX_HANDLERS]; // 事件处理器数组
    event_queue_t queue;        // 事件队列
    uint8_t handler_count;      // 已注册处理器数量
    uint8_t initialized;        // 初始化标志
} event_bus_t;

// 事件总线接口函数声明

/**
 * @brief 初始化事件总线系统
 * @param 无
 * @return event_status_t 初始化状态
 */
event_status_t event_bus_init(void);

/**
 * @brief 注册事件处理器
 * @param event_type 要监听的事件类型
 * @param handler 事件处理函数指针
 * @return event_status_t 注册状态
 */
event_status_t event_bus_subscribe(event_type_t event_type, event_handler_t handler);

/**
 * @brief 注销事件处理器
 * @param event_type 要取消监听的事件类型
 * @param handler 事件处理函数指针
 * @return event_status_t 注销状态
 */
event_status_t event_bus_unsubscribe(event_type_t event_type, event_handler_t handler);

/**
 * @brief 发布事件（同步处理）
 * @param event_type 事件类型
 * @param priority 事件优先级
 * @param data 事件数据指针
 * @param data_size 事件数据大小
 * @return event_status_t 发布状态
 */
event_status_t event_bus_publish(event_type_t event_type, event_priority_t priority, 
                                 const void *data, uint16_t data_size);

/**
 * @brief 发布事件到队列（异步处理）
 * @param event_type 事件类型
 * @param priority 事件优先级
 * @param data 事件数据指针
 * @param data_size 事件数据大小
 * @return event_status_t 发布状态
 */
event_status_t event_bus_post(event_type_t event_type, event_priority_t priority,
                              const void *data, uint16_t data_size);

/**
 * @brief 处理事件队列中的事件
 * @param 无
 * @return event_status_t 处理状态
 */
event_status_t event_bus_process(void);

/**
 * @brief 清空事件队列
 * @param 无
 * @return event_status_t 清空状态
 */
event_status_t event_bus_clear_queue(void);

/**
 * @brief 获取事件队列中待处理事件数量
 * @param 无
 * @return uint8_t 待处理事件数量
 */
uint8_t event_bus_get_pending_count(void);

/**
 * @brief 检查事件总线是否已初始化
 * @param 无
 * @return uint8_t 1-已初始化，0-未初始化
 */
uint8_t event_bus_is_initialized(void);

/**
 * @brief 按优先级处理事件队列（优化版本）
 * @param max_events 最大处理事件数量，0表示处理所有
 * @return event_status_t 处理状态
 */
event_status_t event_bus_process_priority(uint8_t max_events);

/**
 * @brief 获取指定事件类型的处理器数量
 * @param event_type 事件类型
 * @return uint8_t 处理器数量
 */
uint8_t event_bus_get_handler_count(event_type_t event_type);

/**
 * @brief 获取事件总线统计信息
 * @param total_handlers 总处理器数量
 * @param active_handlers 活跃处理器数量
 * @param queue_usage 队列使用率(0-100)
 */
void event_bus_get_stats(uint8_t *total_handlers, uint8_t *active_handlers, uint8_t *queue_usage);

// 便捷宏定义
#define EVENT_PUBLISH_SIMPLE(type) \
    event_bus_publish(type, EVENT_PRIORITY_NORMAL, NULL, 0)

#define EVENT_POST_SIMPLE(type) \
    event_bus_post(type, EVENT_PRIORITY_NORMAL, NULL, 0)

#define EVENT_PUBLISH_WITH_DATA(type, data_ptr, size) \
    event_bus_publish(type, EVENT_PRIORITY_NORMAL, data_ptr, size)

#define EVENT_POST_WITH_DATA(type, data_ptr, size) \
    event_bus_post(type, EVENT_PRIORITY_NORMAL, data_ptr, size)

#ifdef __cplusplus
}
#endif

#endif // __EVENT_BUS_H__
