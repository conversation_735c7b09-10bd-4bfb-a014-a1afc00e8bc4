
#include "event_bus.h"
#include "string.h"

// 全局事件总线实例
static event_bus_t g_event_bus = {0};

// 获取系统时间戳函数（需要根据实际系统实现）
extern uint32_t HAL_GetTick(void);

/**
 * @brief 初始化事件总线系统
 */
event_status_t event_bus_init(void)
{
    // 清零事件总线结构
    memset(&g_event_bus, 0, sizeof(event_bus_t));
    
    // 初始化事件队列
    g_event_bus.queue.front = 0;
    g_event_bus.queue.rear = 0;
    g_event_bus.queue.count = 0;
    
    // 初始化处理器数组
    g_event_bus.handler_count = 0;
    
    // 设置初始化标志
    g_event_bus.initialized = 1;
    
    return EVENT_STATUS_OK;
}

/**
 * @brief 注册事件处理器
 */
event_status_t event_bus_subscribe(event_type_t event_type, event_handler_t handler)
{
    // 检查初始化状态
    if (!g_event_bus.initialized) {
        return EVENT_STATUS_ERROR;
    }
    
    // 检查参数有效性
    if (handler == NULL) {
        return EVENT_STATUS_ERROR;
    }
    
    // 检查是否已达到最大处理器数量
    if (g_event_bus.handler_count >= EVENT_BUS_MAX_HANDLERS) {
        return EVENT_STATUS_ERROR;
    }
    
    // 检查是否已经注册过相同的处理器
    for (uint8_t i = 0; i < g_event_bus.handler_count; i++) {
        if (g_event_bus.handlers[i].event_type == event_type && 
            g_event_bus.handlers[i].handler == handler) {
            // 已存在，只需启用
            g_event_bus.handlers[i].enabled = 1;
            return EVENT_STATUS_OK;
        }
    }
    
    // 添加新的处理器
    g_event_bus.handlers[g_event_bus.handler_count].event_type = event_type;
    g_event_bus.handlers[g_event_bus.handler_count].handler = handler;
    g_event_bus.handlers[g_event_bus.handler_count].enabled = 1;
    g_event_bus.handler_count++;
    
    return EVENT_STATUS_OK;
}

/**
 * @brief 注销事件处理器
 */
event_status_t event_bus_unsubscribe(event_type_t event_type, event_handler_t handler)
{
    // 检查初始化状态
    if (!g_event_bus.initialized) {
        return EVENT_STATUS_ERROR;
    }
    
    // 检查参数有效性
    if (handler == NULL) {
        return EVENT_STATUS_ERROR;
    }
    
    // 查找并禁用处理器
    for (uint8_t i = 0; i < g_event_bus.handler_count; i++) {
        if (g_event_bus.handlers[i].event_type == event_type && 
            g_event_bus.handlers[i].handler == handler) {
            g_event_bus.handlers[i].enabled = 0;
            return EVENT_STATUS_OK;
        }
    }
    
    return EVENT_STATUS_ERROR; // 未找到对应处理器
}

/**
 * @brief 创建事件结构
 */
static void create_event(system_event_t *event, event_type_t event_type, 
                        event_priority_t priority, const void *data, uint16_t data_size)
{
    event->type = event_type;
    event->priority = priority;
    event->timestamp = HAL_GetTick();
    event->data_size = (data_size > EVENT_BUS_MAX_DATA_SIZE) ? EVENT_BUS_MAX_DATA_SIZE : data_size;
    
    if (data != NULL && event->data_size > 0) {
        memcpy(event->data, data, event->data_size);
    }
}

/**
 * @brief 直接处理事件（同步）
 */
static event_status_t process_event_direct(const system_event_t *event)
{
    event_status_t result = EVENT_STATUS_IGNORED;
    
    // 遍历所有处理器，找到匹配的事件类型
    for (uint8_t i = 0; i < g_event_bus.handler_count; i++) {
        if (g_event_bus.handlers[i].enabled && 
            g_event_bus.handlers[i].event_type == event->type) {
            // 调用处理器
            event_status_t handler_result = g_event_bus.handlers[i].handler(event);
            if (handler_result == EVENT_STATUS_OK) {
                result = EVENT_STATUS_OK;
            }
        }
    }
    
    return result;
}

/**
 * @brief 发布事件（同步处理）
 */
event_status_t event_bus_publish(event_type_t event_type, event_priority_t priority, 
                                 const void *data, uint16_t data_size)
{
    // 检查初始化状态
    if (!g_event_bus.initialized) {
        return EVENT_STATUS_ERROR;
    }
    
    // 创建事件
    system_event_t event;
    create_event(&event, event_type, priority, data, data_size);
    
    // 直接处理事件
    return process_event_direct(&event);
}

/**
 * @brief 检查队列是否已满
 */
static uint8_t is_queue_full(void)
{
    return g_event_bus.queue.count >= EVENT_BUS_QUEUE_SIZE;
}

/**
 * @brief 检查队列是否为空
 */
static uint8_t is_queue_empty(void)
{
    return g_event_bus.queue.count == 0;
}

/**
 * @brief 发布事件到队列（异步处理）
 */
event_status_t event_bus_post(event_type_t event_type, event_priority_t priority,
                              const void *data, uint16_t data_size)
{
    // 检查初始化状态
    if (!g_event_bus.initialized) {
        return EVENT_STATUS_ERROR;
    }
    
    // 检查队列是否已满
    if (is_queue_full()) {
        return EVENT_STATUS_ERROR;
    }
    
    // 创建事件并添加到队列
    create_event(&g_event_bus.queue.events[g_event_bus.queue.rear], 
                 event_type, priority, data, data_size);
    
    // 更新队列指针
    g_event_bus.queue.rear = (g_event_bus.queue.rear + 1) % EVENT_BUS_QUEUE_SIZE;
    g_event_bus.queue.count++;
    
    return EVENT_STATUS_OK;
}

/**
 * @brief 从队列中获取下一个事件
 */
static event_status_t dequeue_event(system_event_t *event)
{
    if (is_queue_empty()) {
        return EVENT_STATUS_ERROR;
    }
    
    // 复制事件数据
    memcpy(event, &g_event_bus.queue.events[g_event_bus.queue.front], sizeof(system_event_t));
    
    // 更新队列指针
    g_event_bus.queue.front = (g_event_bus.queue.front + 1) % EVENT_BUS_QUEUE_SIZE;
    g_event_bus.queue.count--;
    
    return EVENT_STATUS_OK;
}

/**
 * @brief 处理事件队列中的事件
 */
event_status_t event_bus_process(void)
{
    // 检查初始化状态
    if (!g_event_bus.initialized) {
        return EVENT_STATUS_ERROR;
    }
    
    system_event_t event;
    event_status_t result = EVENT_STATUS_OK;
    
    // 处理队列中的所有事件
    while (dequeue_event(&event) == EVENT_STATUS_OK) {
        event_status_t process_result = process_event_direct(&event);
        if (process_result != EVENT_STATUS_OK && process_result != EVENT_STATUS_IGNORED) {
            result = process_result;
        }
    }
    
    return result;
}

/**
 * @brief 清空事件队列
 */
event_status_t event_bus_clear_queue(void)
{
    if (!g_event_bus.initialized) {
        return EVENT_STATUS_ERROR;
    }
    
    g_event_bus.queue.front = 0;
    g_event_bus.queue.rear = 0;
    g_event_bus.queue.count = 0;
    
    return EVENT_STATUS_OK;
}

/**
 * @brief 获取事件队列中待处理事件数量
 */
uint8_t event_bus_get_pending_count(void)
{
    if (!g_event_bus.initialized) {
        return 0;
    }
    
    return g_event_bus.queue.count;
}

/**
 * @brief 检查事件总线是否已初始化
 */
uint8_t event_bus_is_initialized(void)
{
    return g_event_bus.initialized;
}

/**
 * @brief 按优先级处理事件队列（优化版本）
 * @param max_events 最大处理事件数量，0表示处理所有
 * @return event_status_t 处理状态
 */
event_status_t event_bus_process_priority(uint8_t max_events)
{
    if (!g_event_bus.initialized) {
        return EVENT_STATUS_ERROR;
    }

    uint8_t processed = 0;
    event_status_t result = EVENT_STATUS_OK;

    // 优先处理高优先级事件
    for (event_priority_t priority = EVENT_PRIORITY_CRITICAL;
         priority >= EVENT_PRIORITY_LOW && (max_events == 0 || processed < max_events);
         priority--) {

        // 扫描队列中的高优先级事件
        for (uint8_t i = 0; i < g_event_bus.queue.count && (max_events == 0 || processed < max_events); i++) {
            uint8_t index = (g_event_bus.queue.front + i) % EVENT_BUS_QUEUE_SIZE;

            if (g_event_bus.queue.events[index].priority == priority) {
                // 处理该事件
                event_status_t process_result = process_event_direct(&g_event_bus.queue.events[index]);
                if (process_result != EVENT_STATUS_OK && process_result != EVENT_STATUS_IGNORED) {
                    result = process_result;
                }

                // 从队列中移除该事件（通过移动其他事件）
                for (uint8_t j = i; j < g_event_bus.queue.count - 1; j++) {
                    uint8_t curr_index = (g_event_bus.queue.front + j) % EVENT_BUS_QUEUE_SIZE;
                    uint8_t next_index = (g_event_bus.queue.front + j + 1) % EVENT_BUS_QUEUE_SIZE;
                    memcpy(&g_event_bus.queue.events[curr_index],
                           &g_event_bus.queue.events[next_index],
                           sizeof(system_event_t));
                }

                g_event_bus.queue.count--;
                g_event_bus.queue.rear = (g_event_bus.queue.rear - 1 + EVENT_BUS_QUEUE_SIZE) % EVENT_BUS_QUEUE_SIZE;
                processed++;
                i--; // 重新检查当前位置
            }
        }
    }

    return result;
}

/**
 * @brief 获取指定事件类型的处理器数量
 * @param event_type 事件类型
 * @return uint8_t 处理器数量
 */
uint8_t event_bus_get_handler_count(event_type_t event_type)
{
    if (!g_event_bus.initialized) {
        return 0;
    }

    uint8_t count = 0;
    for (uint8_t i = 0; i < g_event_bus.handler_count; i++) {
        if (g_event_bus.handlers[i].enabled &&
            g_event_bus.handlers[i].event_type == event_type) {
            count++;
        }
    }

    return count;
}

/**
 * @brief 获取事件总线统计信息
 * @param total_handlers 总处理器数量
 * @param active_handlers 活跃处理器数量
 * @param queue_usage 队列使用率(0-100)
 */
void event_bus_get_stats(uint8_t *total_handlers, uint8_t *active_handlers, uint8_t *queue_usage)
{
    if (!g_event_bus.initialized) {
        if (total_handlers) *total_handlers = 0;
        if (active_handlers) *active_handlers = 0;
        if (queue_usage) *queue_usage = 0;
        return;
    }

    if (total_handlers) {
        *total_handlers = g_event_bus.handler_count;
    }

    if (active_handlers) {
        uint8_t active = 0;
        for (uint8_t i = 0; i < g_event_bus.handler_count; i++) {
            if (g_event_bus.handlers[i].enabled) {
                active++;
            }
        }
        *active_handlers = active;
    }

    if (queue_usage) {
        *queue_usage = (g_event_bus.queue.count * 100) / EVENT_BUS_QUEUE_SIZE;
    }
}
